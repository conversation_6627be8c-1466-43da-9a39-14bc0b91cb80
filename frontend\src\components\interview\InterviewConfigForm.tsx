import React, { useState, useEffect, useCallback, useRef } from 'react';
import { ChevronDown, Upload, FileText, Monitor, CheckCircle, XCircle, AlertTriangle, Power, PowerOff, Mic } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import useResumeStore from '../../stores/resumeStore';
import useInterviewStore, { ScreenShareStatus } from '../../stores/interviewStore';
import { useMockInterviewStore, useMockConfig, useMockConfigActions } from '../../stores/mockInterviewStore';
import useAuthStore from '../../stores/authStore';
import { type TargetPosition } from '../../lib/api/apiService';
import { usePositions, usePositionsLoading, usePositionActions } from '../../stores/positionStore';
import { checkMicrophonePermission, type AudioPermissionResult } from '../../utils/audioPermissions';
import { useToastContext } from '../../contexts/ToastContext';
import { useBalance } from '../../hooks/useBalance';
import { checkAndDeductCredits } from '../../lib/api/credits';
import { InterviewSessionManager } from '../../managers/InterviewSessionManager';
import { globalPerformanceManager } from '../../utils/PerformanceMonitor';
import { useSessionPhase, useOverallProgress, useIsSessionReady } from '../../stores/interviewSessionStore';
import InterviewPreparationProgress from './InterviewPreparationProgress';

interface InterviewConfigFormProps {
  mode?: 'live' | 'mock';
}

const InterviewConfigForm: React.FC<InterviewConfigFormProps> = ({ mode = 'live' }) => {
  const navigate = useNavigate();
  const { showSuccess, showError } = useToastContext();
  const { hasEnoughCredits, deductCredits } = useBalance();
  const { uploadedResume, resumes, loadResumes, getCurrentDisplayResume, hasAnyResume } = useResumeStore();
  const { isAuthenticated } = useAuthStore();

  // 新架构状态管理
  const sessionPhase = useSessionPhase();
  const overallProgress = useOverallProgress();
  const isSessionReady = useIsSessionReady();
  const sessionManagerRef = useRef<InterviewSessionManager | null>(null);
  const performanceMonitorRef = useRef<any>(null);
  // 根据模式选择不同的状态管理
  const liveStore = useInterviewStore();
  const mockStore = useMockConfigActions();
  const mockConfig = useMockConfig();

  // 统一的配置和操作接口
  const config = mode === 'mock' ? mockConfig : liveStore.config;
  const setSelectedPosition = mode === 'mock' ? mockStore.setSelectedPosition : liveStore.setSelectedPosition;
  const setInterviewLanguage = mode === 'mock' ? mockStore.setInterviewLanguage : liveStore.setInterviewLanguage;
  const setAnswerStyle = mode === 'mock' ? mockStore.setAnswerStyle : liveStore.setAnswerStyle;
  const setSecondaryScreen = mode === 'mock' ? () => {} : liveStore.setSecondaryScreen; // mock模式不需要副屏
  const setScreenShareStatus = mode === 'mock' ? mockStore.setScreenShareStatus : liveStore.setScreenShareStatus;
  const setSharedStream = mode === 'mock' ? mockStore.setSharedStream : liveStore.setSharedStream;

  // 🎯 使用全局状态管理替代本地状态
  const positions = usePositions();
  const loadingPositions = usePositionsLoading();
  const { fetchPositions } = usePositionActions();
  const [isStartingInterview, setIsStartingInterview] = useState(false);
  const [micPermissionResult, setMicPermissionResult] = useState<AudioPermissionResult | null>(null);

  // 新架构状态
  const [showPreparationProgress, setShowPreparationProgress] = useState(false);
  const [preparationError, setPreparationError] = useState<string | null>(null);

  // 倒计时状态
  const [countdown, setCountdown] = useState<number | null>(null);
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 音频波形相关状态
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const detectionIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 🎯 使用全局状态管理加载岗位列表
  useEffect(() => {
    const loadPositions = async () => {
      // 只在用户已登录时加载岗位列表
      if (!isAuthenticated) {
        console.log('用户未登录，跳过加载岗位列表');
        return;
      }

      try {
        // 使用全局状态管理的智能缓存
        await fetchPositions();
      } catch (error) {
        console.error('Failed to load positions in InterviewConfigForm:', error);
        // 错误已经在store中处理，这里只需要记录日志
      }
    };

    loadPositions();
  }, [isAuthenticated, fetchPositions]);

  // 🎯 加载用户简历数据
  useEffect(() => {
    const loadUserResumes = async () => {
      // 只在用户已登录时加载简历数据
      if (!isAuthenticated) {
        console.log('用户未登录，跳过加载简历数据');
        return;
      }

      try {
        await loadResumes();
        console.log('InterviewConfigForm: 简历数据加载成功');
      } catch (error) {
        console.error('InterviewConfigForm: 加载简历数据失败:', error);
        // 错误已经在store中处理，这里只需要记录日志
      }
    };

    loadUserResumes();
  }, [isAuthenticated, loadResumes]);

  // 🎯 自动选择第一个岗位（独立的effect）
  useEffect(() => {
    if (positions.length > 0 && !config.selectedPositionId) {
      console.log('自动选择第一个岗位:', positions[0].id);
      setSelectedPosition(positions[0].id);
    }
  }, [positions, config.selectedPositionId, setSelectedPosition]);

  // 简化初始化 - 移除预热逻辑，改为按需创建
  useEffect(() => {
    const initializeSessionManager = () => {
      if (!sessionManagerRef.current) {
        console.log('🔧 InterviewConfigForm: Initializing session manager (no warm-up)');

        // 创建会话管理器实例（不进行预热）
        sessionManagerRef.current = InterviewSessionManager.getInstance();

        console.log('✅ InterviewConfigForm: Session manager initialized');
      }
    };

    initializeSessionManager();

    // 清理函数
    return () => {
      // 简化清理逻辑
      console.log('🧹 InterviewConfigForm: Cleaning up');
    };
  }, []);

  const handleUploadResume = () => {
    navigate('/resume-upload');
  };

  // 检查麦克风权限
  const verifyMicrophone = useCallback(async () => {
    const permission = await checkMicrophonePermission();
    setMicPermissionResult(permission);
    if (!permission.granted) {
      showError(permission.error || '麦克风权限未授予');
    } else {
      showSuccess('麦克风已授权');
    }
    return permission.granted;
  }, [showError, showSuccess]);

  // 设置静态波形
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const setStaticWaveform = useCallback(() => {
    const waveSpans = document.querySelectorAll('.audio-wave span');
    waveSpans.forEach(span => {
      (span as HTMLElement).style.animation = 'none';
      (span as HTMLElement).style.transform = 'scaleY(0.2)';
      (span as HTMLElement).style.height = '15px';
      (span as HTMLElement).style.backgroundColor = '#d9d9d9';
      (span as HTMLElement).style.opacity = '0.5';
      (span as HTMLElement).style.transition = 'transform 0.5s ease';
    });
  }, []);

  // 设置错误波形 - 保留但不使用的函数，可能在将来需要
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const setErrorWaveform = useCallback(() => {
    const waveSpans = document.querySelectorAll('.audio-wave span');
    waveSpans.forEach(span => {
      (span as HTMLElement).style.animation = 'none';
      (span as HTMLElement).style.transform = 'scaleY(0.2)';
      (span as HTMLElement).style.height = '15px';
      (span as HTMLElement).style.backgroundColor = '#ff4d4f';
      (span as HTMLElement).style.opacity = '0.7';
      (span as HTMLElement).style.transition = 'none';
    });
  }, []);

  // 根据音量更新波形显示 - 严格参考 formal.html 的实现，但增强效果
  const updateWaveformBasedOnVolume = useCallback((volume: number) => {
    const waveSpans = document.querySelectorAll('.audio-wave span');
    console.log(`波形更新: 找到 ${waveSpans.length} 个波形元素, 音量: ${volume.toFixed(4)}`);

    if (waveSpans.length === 0) {
      console.warn('未找到波形元素，可能DOM还未渲染完成');
      return;
    }

    const threshold = 0.01; // 音量阈值，低于此值视为无声音

    if (volume > threshold) {
      // 有声音 - 显示动态波形
      console.log('检测到音频，显示动态波形');

      waveSpans.forEach((span) => {
        // 创建一些变化，使波形看起来更自然
        const randomFactor = 0.7 + Math.random() * 0.6;

        // 按照 formal.html 的音量计算逻辑
        const height = Math.min(1, volume * 20) * randomFactor; // 放大音量以便更好地可视化

        // 设置波形高度 - 按照 formal.html 的逻辑
        (span as HTMLElement).style.animation = 'none'; // 移除默认动画
        (span as HTMLElement).style.transform = `scaleY(${Math.max(0.2, height)})`;
        (span as HTMLElement).style.height = '15px'; // 按照 formal.html 的基础高度
        (span as HTMLElement).style.backgroundColor = '#1890ff';
        (span as HTMLElement).style.opacity = '1';
        (span as HTMLElement).style.transition = 'transform 0.1s ease';
      });
    } else {
      // 无声音 - 显示静态虚线波形
      console.log('无音频，显示静态波形');
      waveSpans.forEach(span => {
        (span as HTMLElement).style.animation = 'none';
        (span as HTMLElement).style.transform = 'scaleY(0.2)'; // 按照 formal.html 的静态高度
        (span as HTMLElement).style.height = '15px'; // 按照 formal.html 的基础高度
        (span as HTMLElement).style.backgroundColor = '#d9d9d9';
        (span as HTMLElement).style.opacity = '0.5'; // 按照 formal.html 的透明度
        (span as HTMLElement).style.transition = 'transform 0.5s ease, background-color 0.5s ease, opacity 0.5s ease';
      });
    }
  }, []);

  // 初始化音频处理器 - 严格按照 formal.html 的逻辑实现
  const initAudioProcessor = useCallback((stream: MediaStream) => {
    try {
      console.log("初始化音频处理器...");

      // 创建音频上下文
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();

      // 创建媒体流源
      const source = audioContextRef.current.createMediaStreamSource(stream);

      // 创建分析器
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 256;
      const bufferLength = analyserRef.current.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);

      // 连接节点
      source.connect(analyserRef.current);

      // 清除之前的检测间隔
      if (detectionIntervalRef.current) {
        clearInterval(detectionIntervalRef.current);
      }

      // 设置音频检测间隔 - 完全按照 formal.html 的逻辑
      detectionIntervalRef.current = setInterval(() => {
        // 获取频域数据
        analyserRef.current!.getByteFrequencyData(dataArray);

        // 计算平均音量
        let sum = 0;
        for (let i = 0; i < bufferLength; i++) {
          sum += dataArray[i];
        }
        const average = sum / bufferLength / 255;

        // 更新波形显示
        updateWaveformBasedOnVolume(average);

        // 调试输出
        if (average > 0.01) {
          console.log(`检测到音频: ${average.toFixed(4)}`);
        }
      }, 100);

      console.log('音频处理器初始化成功');
    } catch (error) {
      console.error('初始化音频处理器失败:', error);
    }
  }, [updateWaveformBasedOnVolume]);

  // 清理音频处理器
  const cleanupAudioProcessor = useCallback(() => {
    try {
      if (detectionIntervalRef.current) {
        clearInterval(detectionIntervalRef.current);
        detectionIntervalRef.current = null;
      }

      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close().catch(err => {
          console.warn('关闭音频上下文时出错:', err);
        });
        audioContextRef.current = null;
      }

      analyserRef.current = null;
      console.log('音频处理器已清理');
    } catch (error) {
      console.warn('清理音频处理器时出错:', error);
    }
  }, []);

  // 处理音频权限请求（根据模式选择屏幕共享或麦克风）
  const handleRequestScreenShare = useCallback(async () => {
    if (config.screenShareStatus === 'sharing') { // 如果已在共享，则停止
      if (config.sharedStream) {
        config.sharedStream.getTracks().forEach(track => track.stop());
        console.log('Audio stream stopped by user action.');
      }
      // 清理音频处理器
      cleanupAudioProcessor();
      setSharedStream(null);
      setScreenShareStatus('idle');
      const stopMessage = mode === 'mock' ? '麦克风已停止' : '屏幕共享已停止';
      showSuccess(stopMessage);
      return;
    }

    setScreenShareStatus('pending');
    try {
      let stream: MediaStream;

      if (mode === 'mock') {
        // AI模拟面试：获取麦克风音频
        stream = await navigator.mediaDevices.getUserMedia({
          audio: true,
          video: false
        });
      } else {
        // AI正式面试：获取屏幕共享音频
        stream = await navigator.mediaDevices.getDisplayMedia({
          video: true, // 必须请求视频以共享屏幕
          audio: true  // 同时请求系统音频
        });
      }

      // 监听用户手动停止共享的事件 (例如通过浏览器UI)
      if (mode === 'live' && stream.getVideoTracks()[0]) {
        stream.getVideoTracks()[0].onended = () => {
          console.log('Screen share ended by browser UI.');
          cleanupAudioProcessor(); // 清理音频处理器
          setSharedStream(null); // 清理store中的流
          setScreenShareStatus('idle');
          showSuccess('屏幕共享已结束');
        };
      }

      setSharedStream(stream); // 保存流到Zustand
      setScreenShareStatus('sharing');

      // 初始化音频处理器 - 添加延迟确保DOM已渲染
      if (stream.getAudioTracks().length > 0) {
        console.log('检测到音频轨道，准备初始化音频处理器');
        // 延迟初始化，确保波形DOM元素已经渲染
        setTimeout(() => {
          initAudioProcessor(stream);
        }, 100);
      } else {
        console.warn('未检测到音频轨道');
      }

      const successMessage = mode === 'mock' ? '麦克风音频已开始采集' : '屏幕和系统音频已开始共享';
      showSuccess(successMessage);
    } catch (error) {
      const errorContext = mode === 'mock' ? '麦克风' : '屏幕共享';
      console.error(`获取${errorContext}失败:`, error);

      // 安全地清理资源
      try {
        cleanupAudioProcessor(); // 清理音频处理器
      } catch (cleanupError) {
        console.warn('清理音频处理器时出错:', cleanupError);
      }

      setSharedStream(null); // 确保失败时清除流
      setScreenShareStatus('denied');

      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          const permissionError = mode === 'mock' ? '您已取消或拒绝麦克风权限' : '您已取消或拒绝屏幕共享权限';
          showError(permissionError);
        } else if (error.name === 'NotSupportedError') {
          const supportError = mode === 'mock' ? '您的浏览器不支持麦克风功能' : '您的浏览器不支持屏幕共享功能';
          showError(supportError);
        } else {
          showError(`获取${errorContext}失败，请重试`);
        }
      } else {
        showError(`获取${errorContext}失败，请重试`);
      }
    }
  }, [config.screenShareStatus, config.sharedStream, setSharedStream, setScreenShareStatus, showSuccess, showError, cleanupAudioProcessor, initAudioProcessor]);

  // 倒计时函数
  const startCountdown = useCallback(() => {
    setCountdown(15);

    countdownIntervalRef.current = setInterval(() => {
      setCountdown(prev => {
        if (prev === null || prev <= 1) {
          if (countdownIntervalRef.current) {
            clearInterval(countdownIntervalRef.current);
            countdownIntervalRef.current = null;
          }
          return null;
        }
        return prev - 1;
      });
    }, 1000);
  }, []);

  // 清理倒计时
  const clearCountdown = useCallback(() => {
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
      countdownIntervalRef.current = null;
    }
    setCountdown(null);
  }, []);

  // 组件卸载时清理倒计时
  useEffect(() => {
    return () => {
      clearCountdown();
    };
  }, [clearCountdown]);

  // 使用新的状态管理方法
  const hasResume = hasAnyResume();
  const currentDisplayResume = getCurrentDisplayResume();

  // 检查所有前置条件
  const allPrerequisitesMet =
    isAuthenticated &&
    hasResume && // 个人简历已上传（持久化或临时）
    config.selectedPositionId && // 岗位选择已设置
    config.interviewLanguage && // 面试语言已设置
    config.answerStyle && // 答案风格已设置
    (mode === 'mock' ? true : (config.screenShareStatus === 'sharing' && config.audioCollection)); // mock模式不需要音频验证

  const handleStartInterview = async () => {
    // 检查所有必要条件
    if (!hasResume) {
      showError('请先上传个人简历');
      return;
    }

    if (!config.selectedPositionId) {
      showError('请选择面试岗位');
      return;
    }

    if (!config.interviewLanguage) {
      showError('请选择面试语言');
      return;
    }

    if (!config.answerStyle) {
      showError('请选择答案风格');
      return;
    }

    // 检查面试音频采集（mock模式跳过音频验证）
    if (mode !== 'mock') {
      if (config.screenShareStatus !== 'sharing') {
        showError('请先共享屏幕');
        return;
      }

      if (!config.audioCollection) {
        showError('未检测到系统音频，请确保音频采集成功');
        return;
      }
    }

    // 使用新架构启动面试
    setIsStartingInterview(true);
    setShowPreparationProgress(true);

    // 启动15秒倒计时
    startCountdown();

    if (!sessionManagerRef.current) {
      showError('系统未初始化，请刷新页面重试');
      setIsStartingInterview(false);
      setShowPreparationProgress(false);
      return;
    }

    try {
      // 开始性能监控
      if (performanceMonitorRef.current) {
        performanceMonitorRef.current.startMetric('preparation');
      }

      // 阶段2：准备阶段
      const interviewConfig = {
        selectedPositionId: config.selectedPositionId,
        interviewLanguage: config.interviewLanguage,
        answerStyle: config.answerStyle,
        interviewType: mode === 'mock' ? 'mock' as const : 'formal' as const,
        screenShareStatus: config.screenShareStatus,
        sharedStream: config.sharedStream,
        audioCollection: config.audioCollection
      };

      // 🔥 添加调试日志确认配置正确
      console.log('🔧 InterviewConfigForm: Starting interview with config:', {
        mode,
        interviewType: interviewConfig.interviewType,
        selectedPositionId: interviewConfig.selectedPositionId
      });

      await sessionManagerRef.current.prepare(interviewConfig);

      if (performanceMonitorRef.current) {
        performanceMonitorRef.current.endMetric('preparation');
        performanceMonitorRef.current.startMetric('validation');
      }

      // 阶段3：验证和启动
      await sessionManagerRef.current.start();

      if (performanceMonitorRef.current) {
        performanceMonitorRef.current.endMetric('validation');
      }

      // 根据模式跳转到对应的面试页面
      const targetPath = mode === 'mock' ? '/interview/mock/session' : '/interview/live';

      // 获取选中的岗位信息
      const selectedPosition = positions.find(p => p.id === config.selectedPositionId);

      navigate(targetPath, {
        state: {
          positionId: config.selectedPositionId,
          companyName: selectedPosition?.companyName || '模拟公司',
          positionName: selectedPosition?.positionName || '模拟岗位',
          language: config.interviewLanguage,
          answerStyle: config.answerStyle,
          sessionId: sessionManagerRef.current.getSessionId(),
          mode: mode
        }
      });
    } catch (error: any) {
      console.error('❌ InterviewConfigForm: Failed to start interview:', error);

      // 检查是否是余额不足错误
      if (error.message && error.message.startsWith('INSUFFICIENT_CREDITS:')) {
        const message = error.message.replace('INSUFFICIENT_CREDITS:', '');
        showError(message); // 使用Toast显示余额不足提示
        setShowPreparationProgress(false); // 隐藏准备进度
      } else {
        setPreparationError(error.message || '启动面试失败，请稍后再试');
      }

      setIsStartingInterview(false);

      if (performanceMonitorRef.current) {
        performanceMonitorRef.current.endMetric('preparation', { error: error.message });
      }
    }
  };

  // 处理准备完成
  const handlePreparationReady = useCallback(() => {
    console.log('✅ InterviewConfigForm: Preparation completed, ready to start');
    setShowPreparationProgress(false);
  }, []);

  // 处理准备错误
  const handlePreparationError = useCallback((error: string) => {
    console.error('❌ InterviewConfigForm: Preparation failed:', error);
    setPreparationError(error);
    setIsStartingInterview(false);
    // 不立即隐藏进度，让用户看到错误信息
  }, []);

  // 清理函数：组件卸载时只清理音频处理器，不停止屏幕共享
  // 屏幕共享的生命周期现在完全由Zustand store和面试流程管理
  useEffect(() => {
    return () => {
      // 使用setTimeout确保在下一个事件循环中执行清理，避免与浏览器扩展冲突
      setTimeout(() => {
        try {
          // 只清理音频处理器，不停止屏幕共享
          cleanupAudioProcessor();
          console.log('InterviewConfigForm unmounted, audio processor cleaned up.');
        } catch (error) {
          console.warn('组件卸载清理时出错:', error);
        }
      }, 0);
    };
  }, [cleanupAudioProcessor]);

  const getScreenShareButtonTextAndIcon = () => {
    const isMockMode = mode === 'mock';

    switch (config.screenShareStatus) {
      case 'sharing':
        return {
          text: isMockMode ? '停止麦克风音频' : '停止共享系统音频',
          icon: <PowerOff className="w-5 h-5 mr-2 text-red-500" />,
          color: 'red'
        };
      case 'pending':
        return {
          text: isMockMode ? '正在请求麦克风...' : '正在请求共享...',
          icon: <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500 mr-2"></div>,
          color: 'orange'
        };
      case 'denied':
        return {
          text: isMockMode ? '麦克风被拒绝, 点击重试' : '共享被拒绝, 点击重试',
          icon: <AlertTriangle className="w-5 h-5 mr-2 text-red-500" />,
          color: 'red'
        };
      case 'idle':
      default:
        return {
          text: isMockMode ? '开启麦克风音频' : '共享屏幕和系统音频',
          icon: isMockMode ? <Mic className="w-5 h-5 mr-2" /> : <Monitor className="w-5 h-5 mr-2" />,
          color: 'orange'
        };
    }
  };
  const screenShareButtonInfo = getScreenShareButtonTextAndIcon();

  return (
    <div className="max-w-[520px] w-full bg-white px-6 py-5 rounded-xl flex flex-col shadow-sm overflow-hidden min-h-[600px]">
      <h2 className="text-xl font-semibold mb-4 text-gray-800">个人简历</h2>

      {/* 简历上传状态 */}
      <div className="mb-4">
        {hasResume ? (
          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg bg-gray-50">
            <div className="flex items-center gap-3">
              <FileText className="w-5 h-5 text-gray-600" />
              <span className="text-sm text-gray-800 truncate max-w-[200px] font-medium">
                {currentDisplayResume?.fileName || '未知简历'}
              </span>
              {currentDisplayResume?.source === 'temporary' && (
                <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                  新上传
                </span>
              )}
            </div>
            <button
              onClick={handleUploadResume}
              className="text-blue-600 text-sm hover:text-blue-700 transition-colors font-medium"
            >
              更换
            </button>
          </div>
        ) : (
          <button
            onClick={handleUploadResume}
            className="w-full bg-orange-500 text-white py-3 rounded-lg hover:bg-orange-600 transition-colors text-sm font-medium flex items-center justify-center gap-2"
          >
            <Upload className="w-5 h-5" />
            上传简历
          </button>
        )}
      </div>

      {/* 岗位选择 */}
      <div className="mb-6">
        <label className="block text-sm font-semibold text-gray-700 mb-2">岗位选择</label>
        <div className="relative">
          <select
            value={config.selectedPositionId || ''}
            onChange={(e) => setSelectedPosition(e.target.value || null)}
            className="w-full px-3 py-2.5 bg-white border border-gray-200 rounded-lg text-sm appearance-none focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all"
            disabled={loadingPositions}
          >
            <option value="">请选择</option>
            {positions.map((position) => (
              <option key={position.id} value={position.id} title={`${position.positionName}${position.companyName ? ` - ${position.companyName}` : ''}`}>
                {position.positionName}
                {position.companyName && ` - ${position.companyName}`}
              </option>
            ))}
          </select>
          <ChevronDown className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
        </div>
      </div>

      {/* 面试语言 */}
      <div className="mb-4">
        <label className="block text-sm font-semibold text-gray-700 mb-2">面试语言</label>
        <div className="relative">
          <select
            value={config.interviewLanguage}
            onChange={(e) => setInterviewLanguage(e.target.value as 'chinese' | 'english')}
            className="w-full px-3 py-2.5 bg-white border border-gray-200 rounded-lg text-sm appearance-none focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all"
          >
            <option value="chinese">中文</option>
            <option value="english">英文</option>
          </select>
          <ChevronDown className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
        </div>
      </div>

      {/* 答案风格 */}
      <div className="mb-4">
        <label className="block text-sm font-semibold text-gray-700 mb-2">答案风格</label>
        <div className="relative">
          <select
            value={config.answerStyle}
            onChange={(e) => setAnswerStyle(e.target.value as 'keywords_conversational' | 'conversational')}
            className="w-full px-3 py-2.5 bg-white border border-gray-200 rounded-lg text-sm appearance-none focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all"
          >
            <option value="keywords_conversational">关键词提示 + 口语化答案</option>
            <option value="conversational">口语化答案</option>
          </select>
          <ChevronDown className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
        </div>
      </div>

      {/* 面试副屏 - 只在正式面试模式下显示 */}
      {mode === 'live' && (
        <div className="mb-4 py-2 flex items-center">
          <div className="flex-1">
            <div className="mb-1">
              <label className="text-sm font-semibold text-gray-700">面试副屏</label>
            </div>
            <p className="text-xs text-gray-500">面试副屏专为需要屏幕共享的面试场景设计</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer ml-4">
            <input
              type="checkbox"
              checked={config.secondaryScreen}
              onChange={(e) => setSecondaryScreen(e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-9 h-5 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-500"></div>
          </label>
        </div>
      )}

      {/* 音频采集区域 - mock模式不显示 */}
      {mode !== 'mock' && (
        <div className="mb-4">
          <div className="flex items-center justify-between mb-1">
              <h3 className="text-sm font-semibold text-gray-700">面试音频采集</h3>
            {config.screenShareStatus === 'sharing' && config.audioCollection && (
                <div className="flex items-center text-xs text-green-600">
                    <CheckCircle size={14} className="mr-1" />
                    {mode === 'mock' ? '已开启并采集到音频' : '已共享并采集到音频'}
                </div>
            )}
            {config.screenShareStatus === 'sharing' && !config.audioCollection && (
                <div className="flex items-center text-xs text-yellow-600">
                    <AlertTriangle size={14} className="mr-1" />
                    {mode === 'mock' ? '已开启但未采集到音频' : '已共享但未采集到音频'}
                </div>
            )}
        </div>
        <p className="text-xs text-gray-500 mb-3 leading-relaxed">
          {mode === 'mock'
            ? '面试君通过获取您的麦克风音频，来模拟真实面试场景并提供智能问答。'
            : '面试君通过获取您选择共享的屏幕内容及系统音频，来辅助您进行面试。'
          }
        </p>
        <button
          onClick={handleRequestScreenShare}
          disabled={config.screenShareStatus === 'pending'}
          className={`flex flex-col items-center justify-center w-full py-2.5 rounded-lg text-sm font-medium transition-colors min-h-[80px]
            ${screenShareButtonInfo.color === 'orange' ? 'bg-orange-50 text-orange-600 border border-orange-200 hover:bg-orange-100' : ''}
            ${screenShareButtonInfo.color === 'red' ? 'bg-red-50 text-red-600 border border-red-200 hover:bg-red-100' : ''}
          `}
        >
          {/* 按钮主要内容 */}
          <div className="flex items-center justify-center">
            {screenShareButtonInfo.icon}
            {screenShareButtonInfo.text}
          </div>

          {/* 波形图 - 只在屏幕共享时显示 */}
          {config.screenShareStatus === 'sharing' && (
            <div className="mt-2 w-full">
              <div className="audio-wave flex items-center justify-center space-x-1 h-6">
                {Array.from({ length: 20 }, (_, i) => (
                  <span
                    key={i}
                    className="block w-1 bg-gray-400 rounded-sm transition-all duration-100"
                    style={{
                      height: '15px',
                      transform: 'scaleY(0.2)',
                      opacity: '0.5',
                      transformOrigin: 'bottom'
                    }}
                  ></span>
                ))}
              </div>
              <div className="text-xs text-center mt-1 opacity-75">
                {mode === 'mock' ? '麦克风音频监测中' : '系统音频监测中'}
              </div>
            </div>
          )}
        </button>

         {config.screenShareStatus === 'denied' && (
            <p className="text-xs text-red-500 mt-1">
              {mode === 'mock'
                ? '请在浏览器设置中允许麦克风权限，或检查麦克风设备是否正常。'
                : '请在浏览器设置中允许屏幕共享，或尝试选择其他窗口/屏幕。'
              }
            </p>
        )}
        </div>
      )}

      {/* 底部间距 */}
      <div className="h-4"></div>

      {/* 开始面试按钮 */}
      <button
        onClick={handleStartInterview}
        disabled={isStartingInterview || !allPrerequisitesMet}
        className="w-full bg-gray-800 text-white py-3 rounded-lg text-base font-semibold hover:bg-gray-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed shadow-sm"
      >
        {isStartingInterview ? (
          countdown !== null ? `正在启动... (${countdown}s)` : '正在启动...'
        ) : '开始面试'}
      </button>

      {/* 面试准备进度组件 */}
      <InterviewPreparationProgress
        visible={showPreparationProgress}
        onReady={handlePreparationReady}
        onError={handlePreparationError}
      />
    </div>
  );
};

export default InterviewConfigForm;
